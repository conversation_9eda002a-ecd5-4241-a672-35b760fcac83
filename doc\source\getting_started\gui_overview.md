# GUI Overview

The only difference compared with the original Abaqus GUI is the added toolbar group as shown in Fig.1.2-1. The toolbar group composes of gadget buttons (1, 2), SG creation tool buttons (3, 4, 5, 6, 7, 8), SwiftComp™ analysis and visualization tool buttons (9, 10, 11, 12). Their functions can be simply described as follows:

1. **Work plane**: set sketch plane for 1D/2D customized SGs.
2. **New layups**: add new layups for 1D/2D SGs.
3. **1D SG**: create 1D SG, including common SGs and customized SGs.
4. **2D common SG**: create 2D common SGs.
5. **Assign layups**: create laminate for 2D cross-sections.
6. **Erase layups**: delete laminate for 2D cross-sections.
7. **Read file**: create 2D cross-section from input data file.
8. **3D common SG**: create 3D common SGs.
9. **Homogenization**: carry out homogenization analysis.
10. **Macro model**: import the homogenized properties.
11. **Dehomogenization**: carry out dehomogenization analysis.
12. **Visualization**: visualize the results with only SwiftComp analysis files.

