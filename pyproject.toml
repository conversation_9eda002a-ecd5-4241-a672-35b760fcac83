[project]
name = "msg-abaqus-toolkit"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "sphinx>=4.0",
    "myst-parser>=0.18.0",
    "sphinx-immaterial",
]

[project.scripts]
build-docs = "sphinx-build"

[tool.uv.scripts]
docs-html = "sphinx-build -M html doc/source doc/build"
docs-pdf = "sphinx-build -M latexpdf doc/source doc/build"
