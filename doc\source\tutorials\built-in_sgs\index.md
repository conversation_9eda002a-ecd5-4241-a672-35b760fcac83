# Built-in SGs

Abaqus-SwiftComp GUI provides a convenient way to create some common SG models. Engineers can easily create the geometry of these models, and invoke SwiftComp™ to perform homogenization and dehomogenization for different composites with common SGs.

Currently, Abaqus-SwiftComp GUI provides the following common SG models:

_1D SG: laminate._

_2D SG: square pack microstructure with or without interphase region, hexagonal pack microstructure with or without interphase region._

_3D SG: spherical inclusion microstructure with or without interphase region._

In this chapter, we will use some common SGs for different models (e.g. solid, plate/shell and beam) to illustrate how to use Abaqus-SwiftComp GUI to perform homogenization and dehomogenization.
