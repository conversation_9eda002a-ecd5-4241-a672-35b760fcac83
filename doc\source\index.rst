.. Abaqus-SwiftComp Toolset documentation master file, created by
   sphinx-quickstart on Tue Aug 12 16:26:29 2025.
   You can adapt this file completely to your liking, but it should at least
   contain the root `toctree` directive.

Abaqus-SwiftComp GUI documentation
======================================

Based on the recently invented Mechanics of Structure Genome (MSG), SwiftComp provides an efficient and accurate approach for modeling composite materials and structures.
It can be used either independently as a tool for virtual testing of composites or as a plugin to power conventional FEA codes with high-fidelity multiscale modeling for composites.
SwiftComp implements a true multiscale theory which assures the best models at a given level of efficiency to capture both anisotropy and heterogeneity of composites at the microscopic scale or any other scale of user's interest.
SwiftComp enables engineers to model composites as a black aluminum, capturing details as needed and affordable.
This saves orders of magnitude in computing time and resources without sacrificing accuracy, while enabling engineers to tackle complex problems effectively.

SwiftComp can be used as a standalone code or as a plugin for other commercial codes.
To facilitate the use of SwiftComp, a simple graphic user interface (GUI) with a toolbar integrated the functions of SwiftComp is developed in Abaqus.
This manual focuses on explaining how to use Abaqus-SwiftComp GUI.


..  toctree::
    :maxdepth: 2
    :caption: Contents:

    getting_started/index
    user_guide/index


Indices and tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`

